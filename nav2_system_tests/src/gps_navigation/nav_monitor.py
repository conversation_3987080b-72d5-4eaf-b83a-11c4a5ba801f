#!/usr/bin/env python3

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

"""
导航系统监控节点

持续监控Navigation2系统的关键组件状态，只有当所有必需的导航节点和服务都可用时，
才发布导航就绪事件，触发rviz2的启动。
"""

import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile, ReliabilityPolicy, DurabilityPolicy
from std_msgs.msg import Bool, String
from lifecycle_msgs.srv import GetState
from std_srvs.srv import Trigger
from rclpy.action import ActionClient
from nav2_msgs.action import ComputePathToPose, FollowPath, NavigateToPose, NavigateThroughPoses
from nav2_msgs.action import BackUp, Spin, Wait, DriveOnHeading, AssistedTeleop
import time


class NavMonitor(Node):
    def __init__(self):
        super().__init__('nav_monitor')

        # 参数
        self.declare_parameter('required_nodes', [
            '/controller_server',
            '/planner_server',
            '/smoother_server',
            '/behavior_server',
            '/bt_navigator',
            '/waypoint_follower',
            '/velocity_smoother',
            '/collision_monitor',
            '/lifecycle_manager_navigation'
        ])
        self.declare_parameter('required_actions', [
            '/compute_path_to_pose',
            '/follow_path',
            '/navigate_to_pose',
            '/navigate_through_poses',
            '/backup',
            '/spin',
            '/wait',
            '/drive_on_heading',
            '/assisted_teleop'
        ])
        self.declare_parameter('check_frequency', 1.0)
        self.declare_parameter('service_timeout', 2.0)

        self.required_nodes = self.get_parameter('required_nodes').value
        self.required_actions = self.get_parameter('required_actions').value
        self.check_frequency = self.get_parameter('check_frequency').value
        self.service_timeout = self.get_parameter('service_timeout').value

        # 导航系统状态跟踪
        self.node_status = {node: False for node in self.required_nodes}
        self.action_status = {action: False for action in self.required_actions}

        # 发布导航状态
        self.status_publisher = self.create_publisher(Bool, '/nav_ready', 10)
        self.detail_publisher = self.create_publisher(String, '/nav_status_detail', 10)

        # 状态标志
        self.all_nav_ready = False
        self.ready_event_published = False
        self.exit_when_ready = True  # 导航就绪时自动退出

        # 稳定性检查
        self.stability_check_duration = 3.0  # 检测到就绪后等待3秒确保稳定
        self.first_ready_time = None  # 首次检测到就绪的时间

        # 定时器 - 定期检查导航系统状态
        self.timer = self.create_timer(1.0 / self.check_frequency, self.check_navigation_status)

        # 生命周期状态客户端缓存
        self.lifecycle_clients = {}

        # 生命周期管理器状态客户端缓存
        self.lifecycle_manager_clients = {}

        # 动作客户端缓存 - 用于更准确的可用性检测
        self.action_clients = {}

        # 动作类型映射
        self.action_type_map = {
            '/compute_path_to_pose': ComputePathToPose,
            '/follow_path': FollowPath,
            '/navigate_to_pose': NavigateToPose,
            '/navigate_through_poses': NavigateThroughPoses,
            '/backup': BackUp,
            '/spin': Spin,
            '/wait': Wait,
            '/drive_on_heading': DriveOnHeading,
            '/assisted_teleop': AssistedTeleop
        }

        self.get_logger().info(f'Navigation monitor started.')
        self.get_logger().info(f'Required nodes: {len(self.required_nodes)}')
        self.get_logger().info(f'Required actions: {len(self.required_actions)}')
        self.get_logger().info(f'Check frequency: {self.check_frequency} Hz')

    def check_lifecycle_state(self, node_name):
        """检查生命周期节点的状态是否为 active"""
        try:
            # 确保节点名称以 '/' 开头
            if not node_name.startswith('/'):
                node_name = '/' + node_name

            # 特殊处理生命周期管理器节点
            if 'lifecycle_manager' in node_name:
                return self.check_lifecycle_manager_state(node_name)

            # 创建或获取生命周期状态客户端
            if node_name not in self.lifecycle_clients:
                service_name = f'{node_name}/get_state'
                self.lifecycle_clients[node_name] = self.create_client(GetState, service_name)

            client = self.lifecycle_clients[node_name]

            # 检查服务是否可用（短超时）
            if not client.wait_for_service(timeout_sec=0.5):
                self.get_logger().info(f'❌ Lifecycle service {service_name} not available')
                return False

            # 创建请求
            request = GetState.Request()

            # 同步调用服务（短超时）
            try:
                future = client.call_async(request)
                # 使用 spin_until_future_complete 但设置短超时
                rclpy.spin_until_future_complete(self, future, timeout_sec=1.0)

                if future.done():
                    response = future.result()
                    # 检查状态是否为 active (状态ID为3)
                    is_active = response.current_state.id == 3  # ACTIVE state
                    self.get_logger().info(f'✅ Node {node_name} lifecycle state: {response.current_state.label} (id={response.current_state.id})')
                    return is_active
                else:
                    self.get_logger().warning(f'❌ Timeout checking lifecycle state for {node_name}')
                    return False
            except Exception as e:
                self.get_logger().warning(f'❌ Exception checking lifecycle state for {node_name}: {e}')
                return False

        except Exception as e:
            # 如果出现任何错误，假设节点不是生命周期节点或不可用
            return True  # 对于非生命周期节点，返回True

    def check_lifecycle_manager_state(self, node_name):
        """检查生命周期管理器节点的状态"""
        try:
            # 创建或获取生命周期管理器状态客户端
            if node_name not in self.lifecycle_manager_clients:
                service_name = f'{node_name}/is_active'
                self.lifecycle_manager_clients[node_name] = self.create_client(Trigger, service_name)

            client = self.lifecycle_manager_clients[node_name]

            # 检查服务是否可用（短超时）
            if not client.wait_for_service(timeout_sec=0.5):
                return False

            # 创建请求
            request = Trigger.Request()

            # 同步调用服务（短超时）
            try:
                future = client.call_async(request)
                # 使用 spin_until_future_complete 但设置短超时
                rclpy.spin_until_future_complete(self, future, timeout_sec=1.0)

                if future.done():
                    response = future.result()
                    # 检查是否成功
                    self.get_logger().info(f'✅ Lifecycle manager {node_name} is_active: {response.success}')
                    return response.success
                else:
                    self.get_logger().warning(f'❌ Timeout checking lifecycle manager {node_name}')
                    return False
            except Exception as e:
                self.get_logger().warning(f'❌ Exception checking lifecycle manager {node_name}: {e}')
                return False

        except Exception as e:
            self.get_logger().debug(f'Error checking lifecycle manager {node_name}: {e}')
            return False

    def check_nodes(self):
        """检查所有必需的节点是否运行并且处于正确的生命周期状态"""
        node_names = self.get_node_names()

        # 调试信息：打印一次节点列表
        if not hasattr(self, '_debug_printed'):
            self.get_logger().info(f'Available nodes: {node_names}')
            self._debug_printed = True

        for node in self.required_nodes:
            # 移除前导斜杠进行比较（与check_nav2_services.py保持一致）
            node_name = node.lstrip('/')
            is_running = node_name in node_names

            # 简化检查：只要节点在运行就认为就绪
            # 跳过生命周期状态检查，因为它在某些环境下可能不可靠
            if not is_running:
                # 调试信息：节点未运行
                self.get_logger().debug(f'Node {node_name} not found in node list')

            # 节点只需要运行就算就绪
            is_ready = is_running

            if is_ready and not self.node_status[node]:
                # 节点刚刚变为就绪状态
                self.node_status[node] = True
                self.get_logger().info(f'✅ Node {node} is now RUNNING')
            elif not is_ready and self.node_status[node]:
                # 节点不再就绪
                self.node_status[node] = False
                self.get_logger().warning(f'❌ Node {node} is NOT RUNNING')
            elif not is_ready and not self.node_status[node]:
                # 调试信息：节点仍然未就绪
                self.get_logger().debug(f'Node {node_name} still not running')

    def check_action_server_availability(self, action_name):
        """更准确地检查单个动作服务器是否真正可用"""
        try:
            # 创建或获取动作客户端
            if action_name not in self.action_clients:
                if action_name in self.action_type_map:
                    action_type = self.action_type_map[action_name]
                    self.action_clients[action_name] = ActionClient(self, action_type, action_name)
                else:
                    self.get_logger().warning(f'Unknown action type for {action_name}')
                    return False

            client = self.action_clients[action_name]

            # 检查动作服务器是否可用（短超时）
            return client.wait_for_server(timeout_sec=0.5)

        except Exception as e:
            self.get_logger().debug(f'Error checking action server {action_name}: {e}')
            return False

    def check_actions(self):
        """检查所有必需的动作服务器是否可用"""
        for action in self.required_actions:
            # 使用更准确的检测方法
            action_available = self.check_action_server_availability(action)

            if action_available and not self.action_status[action]:
                # 动作服务器刚刚可用
                self.action_status[action] = True
                self.get_logger().info(f'✅ Action {action} is now AVAILABLE')
            elif not action_available and self.action_status[action]:
                # 动作服务器不可用
                self.action_status[action] = False
                self.get_logger().warning(f'❌ Action {action} is NOT AVAILABLE')

    def check_navigation_status(self):
        """定期检查导航系统状态"""
        # 检查节点状态
        self.check_nodes()

        # 检查动作服务器状态
        self.check_actions()

        # 检查是否所有组件都就绪
        all_nodes_ready = all(self.node_status.values())
        all_actions_ready = all(self.action_status.values())
        all_ready = all_nodes_ready and all_actions_ready

        current_time = self.get_clock().now()

        if all_ready:
            if self.first_ready_time is None:
                # 首次检测到所有组件就绪
                self.first_ready_time = current_time
                self.get_logger().info('🔄 All navigation components detected as ready. Starting stability check...')
            elif not self.all_nav_ready:
                # 检查是否已经稳定足够长时间
                elapsed_time = (current_time - self.first_ready_time).nanoseconds / 1e9
                if elapsed_time >= self.stability_check_duration:
                    # 系统已稳定，发布就绪事件
                    self.all_nav_ready = True
                    self.get_logger().info(f'✅ ALL NAVIGATION COMPONENTS ARE STABLE FOR {elapsed_time:.1f}s! Publishing ready event...')
                    self.publish_ready_event()

                    # 如果设置为导航就绪时退出，则退出节点
                    if self.exit_when_ready:
                        # 延迟退出，确保消息发布完成
                        self.create_timer(1.0, self.exit_node)
                else:
                    # 仍在稳定性检查期间
                    remaining_time = self.stability_check_duration - elapsed_time
                    self.get_logger().info(f'⏳ Stability check in progress... {remaining_time:.1f}s remaining')
        else:
            # 有组件不就绪，重置稳定性检查
            if self.first_ready_time is not None:
                self.get_logger().warning('⚠️ Components became unavailable during stability check. Resetting...')
                self.first_ready_time = None

            if self.all_nav_ready:
                # 有组件失效
                self.all_nav_ready = False
                self.ready_event_published = False
                not_ready_nodes = [name for name, status in self.node_status.items() if not status]
                not_ready_actions = [name for name, status in self.action_status.items() if not status]

                if not_ready_nodes:
                    self.get_logger().warning(f'Nodes not ready: {not_ready_nodes}')
                if not_ready_actions:
                    self.get_logger().warning(f'Actions not ready: {not_ready_actions}')

        # 发布状态信息
        self.publish_status()

    def publish_ready_event(self):
        """发布导航就绪事件"""
        if not self.ready_event_published:
            # 发布布尔状态
            ready_msg = Bool()
            ready_msg.data = True
            self.status_publisher.publish(ready_msg)

            # 发布详细状态
            detail_msg = String()
            detail_msg.data = "ALL_NAVIGATION_COMPONENTS_READY"
            self.detail_publisher.publish(detail_msg)

            self.ready_event_published = True

            # 持续发布就绪状态，确保订阅者能收到
            for i in range(5):  # 发布5次确保可靠性
                self.status_publisher.publish(ready_msg)
                time.sleep(0.1)

    def publish_status(self):
        """发布当前导航系统状态"""
        # 发布布尔状态
        ready_msg = Bool()
        ready_msg.data = self.all_nav_ready
        self.status_publisher.publish(ready_msg)

        # 发布详细状态
        detail_msg = String()
        node_status_list = [f"{name.split('/')[-1]}:{'✓' if status else '✗'}"
                           for name, status in self.node_status.items()]
        action_status_list = [f"{name.split('/')[-1]}:{'✓' if status else '✗'}"
                             for name, status in self.action_status.items()]

        detail_msg.data = f"Nodes[{', '.join(node_status_list)}] Actions[{', '.join(action_status_list)}]"
        self.detail_publisher.publish(detail_msg)

    def exit_node(self):
        """退出节点以触发OnExecutionComplete事件"""
        self.get_logger().info('✅ Navigation monitor task completed. Exiting...')
        # 停止定时器
        if hasattr(self, 'timer'):
            self.timer.cancel()
        # 触发节点退出
        import sys
        sys.exit(0)


def main(args=None):
    rclpy.init(args=args)

    nav_monitor = NavMonitor()

    try:
        rclpy.spin(nav_monitor)
    except KeyboardInterrupt:
        pass
    finally:
        nav_monitor.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
